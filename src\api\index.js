/**
 * API统一入口文件
 * 导出所有API模块，方便在组件中使用
 */

// 导入基础请求模块
import soapClient, { sendSoapRequest } from './request.js'

// 导入具体业务API模块
import departmentAPI from './department.js'
import doctorAPI from './doctor.js'
import scheduleAPI from './schedule.js'
import priceAPI from './price.js'

// 统一导出所有API
export {
  // 基础请求方法
  soapClient,
  sendSoapRequest,

  // 业务API模块
  departmentAPI,
  doctorAPI,
  scheduleAPI,
  priceAPI
}

// 也可以按业务模块分组导出
export const API = {
  // 基础请求
  soap: {
    client: soapClient,
    sendRequest: sendSoapRequest
  },

  // 科室相关API
  department: departmentAPI,

  // 医生相关API
  doctor: doctorAPI,

  // 排班相关API
  schedule: scheduleAPI,

  // 物价相关API
  price: priceAPI
}

// 默认导出API对象
export default API
